import telebot
from telebot.types import Message, InputMediaVideo, InputMediaPhoto, InputMediaDocument
import datetime
import json
import time
import threading
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor

BOT_TOKEN = '7662762804:AAFtvLiB-nj9sv9VGQKyCgMyvef4mMriXY'
ADMIN_IDS = [6445842390]
USER_DATA_FILE = 'user_data.json'
MEDIA_ID_MAPPING_FILE = 'media_id_mapping.json'
BANNED_USERS_FILE = 'banned_users.json'

bot = telebot.TeleBot(BOT_TOKEN)

active_users = {}
banned_users = set()
media_id_mapping = {}
media_id_counter = 0
user_names = {}
user_media_groups = {}
media_history = []

lock = threading.Lock()
ASYNC_LOOP = asyncio.new_event_loop()
executor = ThreadPoolExecutor()
SEND_RATE_LIMIT = 25
last_sent_time = []

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('bot.log'), logging.StreamHandler()])
logger = logging.getLogger(__name__)

def load_json(path, default):
    try:
        with open(path, 'r') as f:
            return json.load(f)
    except:
        return default

def save_json(path, data):
    try:
        with open(path, 'w') as f:
            json.dump(data, f, indent=4)
    except Exception as e:
        logger.error(f"保存失败: {path}, {e}")

def load_all():
    global active_users, banned_users, media_id_mapping, media_id_counter
    data = load_json(USER_DATA_FILE, {})
    active_users.update({int(k): datetime.datetime.fromisoformat(v) for k, v in data.get("active_users", {}).items()})
    user_names.update(data.get("user_names", {}))
    banned_users.update(load_json(BANNED_USERS_FILE, []))
    media_id_mapping.update(load_json(MEDIA_ID_MAPPING_FILE, {}))
    if media_id_mapping:
        media_id_counter = max(map(int, media_id_mapping.keys())) + 1

def save_all():
    try:
        save_json(USER_DATA_FILE, {
            "active_users": {str(k): v.isoformat() for k, v in active_users.copy().items()},
            "user_names": user_names.copy()
        })
        save_json(BANNED_USERS_FILE, list(banned_users))
        save_json(MEDIA_ID_MAPPING_FILE, media_id_mapping.copy())
    except Exception as e:
        logger.error(f"保存失败: {e}")

def is_user_blocked(user_id):
    try:
        bot.send_chat_action(user_id, 'typing')
        return False
    except Exception as e:
        return '403' in str(e) or 'blocked' in str(e).lower()

def is_user_active(user_id):
    if user_id in active_users:
        return (datetime.datetime.now() - active_users[user_id]).total_seconds() < 7200
    return False

def get_user_name(user_id):
    return user_names.get(str(user_id), "匿名用户")

async def dispatch_media_to_users(uid, media_list):
    user_ids = [
        user_id for user_id in list(active_users)
        if user_id != uid and user_id not in banned_users and is_user_active(user_id)
    ]
    
    batch_size = 10
    for i in range(0, len(user_ids), batch_size):
        batch = user_ids[i:i + batch_size]
        tasks = []

        for user_id in batch:
            tasks.append(asyncio.create_task(send_media_to_user(user_id, media_list)))

        await asyncio.gather(*tasks)
        await asyncio.sleep(1)  # 每批之间等待 1 秒

async def send_media_to_user(user_id, media_list):
    try:
        for media in media_list:
            if isinstance(media, InputMediaPhoto):
                bot.send_photo(user_id, media.media, caption=media.caption)
            elif isinstance(media, InputMediaVideo):
                bot.send_video(user_id, media.media, caption=media.caption)
            elif isinstance(media, InputMediaDocument):
                bot.send_document(user_id, media.media, caption=media.caption)
            await asyncio.sleep(0.1)  # 控制每用户发送间隔
    except Exception as e:
        logger.warning(f"发送失败 {user_id}: {e}")
        with open("send_failures.log", "a") as f:
            f.write(f"{datetime.datetime.now()} - 发送失败: {user_id} - {e}\n")
        if '403' in str(e) or 'blocked' in str(e).lower():
            active_users.pop(user_id, None)

def forward_user_media_group(uid, media_id):
    group = user_media_groups.pop(uid, [])
    if not group:
        return
    items = []
    caption = f"来自 {get_user_name(uid)} #编号:{media_id}"
    for m in group:
        if m.content_type == 'photo':
            items.append(InputMediaPhoto(m.photo[-1].file_id, caption=caption))
        elif m.content_type == 'video':
            items.append(InputMediaVideo(m.video.file_id, caption=caption))
        elif m.content_type == 'document':
            items.append(InputMediaDocument(m.document.file_id, caption=caption))
    asyncio.run_coroutine_threadsafe(dispatch_media_to_users(uid, items), ASYNC_LOOP)

@bot.message_handler(commands=['start'])
def cmd_start(msg):
    bot.send_message(msg.chat.id, "欢迎使用匿名广播机器人~\n发送 /join 开始使用，发送媒体后将自动广播。")

@bot.message_handler(commands=['join'])
def cmd_join(msg):
    if msg.from_user.id in banned_users:
        return bot.reply_to(msg, "您已被封禁，无法使用。")
    delta = datetime.timedelta(minutes=99999999 if msg.from_user.id in ADMIN_IDS else 30)
    active_users[msg.from_user.id] = datetime.datetime.now() + delta
    save_all()
    bot.reply_to(msg, "已加入，发送媒体将自动广播。")

@bot.message_handler(commands=['checkactivity'])
def cmd_check(msg):
    if msg.from_user.id not in active_users:
        return bot.reply_to(msg, "您尚未加入，发送 /join 先加入。")
    remain = (active_users[msg.from_user.id] - datetime.datetime.now()).total_seconds()
    if remain < 0:
        bot.reply_to(msg, "您已过期，重新发送媒体激活。")
    else:
        bot.reply_to(msg, f"您的活跃时间剩余：{int(remain // 60)} 分钟")

@bot.message_handler(commands=['setmyname'])
def cmd_setname(msg):
    name = msg.text.replace('/setmyname', '').strip()
    if name:
        user_names[str(msg.from_user.id)] = name
        save_all()
        bot.reply_to(msg, f"设置成功，您的名称：{name}")
    else:
        bot.reply_to(msg, "用法：/setmyname 你的昵称")

@bot.message_handler(commands=['getmyname'])
def cmd_getname(msg):
    bot.reply_to(msg, f"您的名字是：{get_user_name(msg.from_user.id)}")

@bot.message_handler(commands=['ban'])
def cmd_ban(msg):
    if msg.from_user.id not in ADMIN_IDS:
        return bot.reply_to(msg, "您无权限执行此操作。")
    if msg.reply_to_message and msg.reply_to_message.caption:
        parts = msg.reply_to_message.caption.split('#编号:')
        if len(parts) > 1:
            media_id = parts[1].strip()
            user_id = media_id_mapping.get(media_id, {}).get("user_id")
            if user_id:
                banned_users.add(user_id)
                save_all()
                return bot.reply_to(msg, f"用户 {user_id} 已封禁。")
    bot.reply_to(msg, "请回复带编号的媒体进行封禁。")
    
    
@bot.message_handler(commands=['jiang'])
def cmd_reward_media(msg):
    if msg.from_user.id not in ADMIN_IDS:
        return bot.reply_to(msg, "您无权限执行此操作。")
    
    # 必须是回复带编号的消息
    if not msg.reply_to_message or not msg.reply_to_message.caption:
        return bot.reply_to(msg, "请回复要奖励的媒体消息。")
    
    parts = msg.reply_to_message.caption.split('#编号:')
    if len(parts) < 2:
        return bot.reply_to(msg, "未检测到编号，请确认该媒体说明中含有 #编号:x")

    media_id = parts[1].strip()
    info = media_id_mapping.get(media_id)
    if not info:
        return bot.reply_to(msg, f"未找到编号 {media_id} 对应的用户。")

    user_id = info.get("user_id")
    if not user_id:
        return bot.reply_to(msg, f"编号 {media_id} 未关联任何用户。")

    # 增加300小时 = 300 * 3600秒
    expire = active_users.get(user_id, datetime.datetime.now())
    active_users[user_id] = max(datetime.datetime.now(), expire) + datetime.timedelta(hours=300)
    save_all()

    # 通知所有用户
    text = f"📢 编号（{media_id}）的用户「{get_user_name(user_id)}」发送的媒体非常棒👍🏻，奖励300小时时长！\n如果你有频道/群组 想有个人的媒体共享机器人 联系 @luoliso3 免费搭建\nIf you have a channel/group and want a personal media sharing bot, please contact @luoliso3 for free setup"
    for uid in list(active_users.keys()):
        try:
            bot.send_message(uid, text)
        except Exception as e:
            logger.warning(f"通知失败 {uid}: {e}")

    # 如果你想让它通知管理员置顶，也可以加上
    try:
        for admin_id in ADMIN_IDS:
            bot.send_message(admin_id, text)
    except:
        pass

    bot.reply_to(msg, f"🎉 成功奖励用户 {user_id}，已通知所有用户。")
    
    
@bot.message_handler(commands=['unban'])
def cmd_unban(msg):
    if msg.from_user.id not in ADMIN_IDS:
        return bot.reply_to(msg, "您无权限执行此操作。")
    parts = msg.text.split()
    if len(parts) >= 2:
        uid = int(parts[1])
        banned_users.discard(uid)
        save_all()
        bot.reply_to(msg, f"用户 {uid} 已解除封禁。")

@bot.message_handler(commands=['listusers'])
def cmd_users(msg):
    bot.reply_to(msg, f"当前活跃用户数: {len(active_users)}")

@bot.message_handler(content_types=['photo', 'video', 'document'])
def handle_media(msg):
    global media_id_counter
    if msg.from_user.id in banned_users:
        return bot.reply_to(msg, "您已被封禁。")
    with lock:
        expire = active_users.get(msg.from_user.id, datetime.datetime.now())
        active_users[msg.from_user.id] = max(datetime.datetime.now(), expire) + datetime.timedelta(minutes=30)
        media_id_counter += 1
        media_id = str(media_id_counter)
        media_id_mapping[media_id] = {
            "user_id": msg.from_user.id,
            "message_id": msg.message_id,
            "chat_id": msg.chat.id
        }
        user_media_groups.setdefault(msg.from_user.id, []).append(msg)
        if len(user_media_groups[msg.from_user.id]) >= 2 or msg.content_type != 'photo':
            forward_user_media_group(msg.from_user.id, media_id)
        if len(media_history) < 500:
            media_history.append(msg)
        save_all()

def clean_loop():
    while True:
        now = datetime.datetime.now()
        expired = [uid for uid, ts in active_users.items() if (now - ts).total_seconds() > 7200]
        for uid in expired:
            active_users.pop(uid, None)
        save_all()
        time.sleep(60)

if __name__ == '__main__':
    load_all()
    for admin in ADMIN_IDS:
        try:
            bot.send_message(admin, "✅ 广播机器人已启动并加载数据")
        except:
            pass
    threading.Thread(target=clean_loop, daemon=True).start()
    threading.Thread(target=ASYNC_LOOP.run_forever, daemon=True).start()
    bot.polling()
